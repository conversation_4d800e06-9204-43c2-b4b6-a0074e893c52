# ToolBox Application Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Environment
NODE_ENV=development
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# NextAuth.js Configuration
NEXTAUTH_SECRET=your-super-secure-secret-key-minimum-32-characters
NEXTAUTH_URL=http://localhost:3000

# Google OAuth (for authentication)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB Connection
MONGODB_URI=mongodb+srv://username:<EMAIL>/toolbox?retryWrites=true&w=majority

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================

# Upstash Redis for Rate Limiting (Production)
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# Security Configuration
CSRF_SECRET=your-csrf-secret-key-minimum-32-characters

# =============================================================================
# FILE STORAGE & MEDIA
# =============================================================================

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

# SMTP Configuration for contact forms
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# =============================================================================
# ANALYTICS & MONITORING (Optional)
# =============================================================================

# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Performance Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug Settings (Development Only)
DEBUG_MODE=false
VERBOSE_LOGGING=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Production Security Headers
ENABLE_STRICT_CSP=true
ENABLE_HSTS=true
ENABLE_SECURITY_HEADERS=true

# Performance Settings
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=300

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/Disable Features
ENABLE_CONTACT_FORM=true
ENABLE_BLOG_SYSTEM=true
ENABLE_USER_REGISTRATION=true
ENABLE_ADMIN_PANEL=true
ENABLE_PDF_TOOLS=true
ENABLE_CALCULATORS=true

# =============================================================================
# API CONFIGURATION
# =============================================================================

# External API Keys (if needed)
EXTERNAL_API_KEY=your-external-api-key
EXTERNAL_API_SECRET=your-external-api-secret

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# =============================================================================
# NOTES
# =============================================================================

# Security Notes:
# - Never commit this file with real values to version control
# - Use strong, unique passwords and secrets
# - Rotate secrets regularly in production
# - Use environment-specific values for each deployment

# Required for Production:
# - NEXTAUTH_SECRET (minimum 32 characters)
# - MONGODB_URI (valid MongoDB connection string)
# - UPSTASH_REDIS_REST_URL and TOKEN (for rate limiting)
# - GOOGLE_CLIENT_ID and SECRET (for OAuth)

# Optional but Recommended:
# - CLOUDINARY_* (for image uploads)
# - SMTP_* (for email functionality)
# - Analytics and monitoring tools

# Development vs Production:
# - Use localhost URLs for development
# - Use HTTPS URLs for production
# - Enable strict security headers in production
# - Use Redis rate limiting in production
