import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Contact from '@/models/Contact';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectDB();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const category = searchParams.get('category') || 'all';
    const priority = searchParams.get('priority') || 'all';
    const assignedTo = searchParams.get('assignedTo') || 'all';
    const dateRange = searchParams.get('dateRange') || 'all';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query: any = {};

    // Search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } },
      ];
    }

    // Status filter
    if (status !== 'all') {
      query.status = status;
    }

    // Category filter
    if (category !== 'all') {
      query.category = category;
    }

    // Priority filter
    if (priority !== 'all') {
      query.priority = priority;
    }

    // Assigned to filter
    if (assignedTo !== 'all') {
      if (assignedTo === 'unassigned') {
        query.assignedTo = { $exists: false };
      } else {
        query.assignedTo = assignedTo;
      }
    }

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }

      query.createdAt = { $gte: startDate };
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Fetch contacts
    const contacts = await Contact.find(query)
      .populate('assignedTo', 'name email')
      .sort(sort)
      .lean();

    // Format data for export
    const exportData = contacts.map(contact => ({
      'Name': contact.name,
      'Email': contact.email,
      'Category': contact.category,
      'Message': contact.message,
      'Status': contact.status,
      'Priority': contact.priority,
      'Assigned To': contact.assignedTo ? `${contact.assignedTo.name} (${contact.assignedTo.email})` : 'Unassigned',
      'Response Sent': contact.responseSent ? 'Yes' : 'No',
      'Created At': new Date(contact.createdAt).toLocaleString(),
      'Updated At': new Date(contact.updatedAt).toLocaleString(),
    }));

    if (format === 'csv') {
      // Generate CSV
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header] || '';
            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="contacts-export-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    } else if (format === 'excel') {
      // For Excel format, we'll return CSV with Excel-specific headers
      // In a real implementation, you might want to use a library like xlsx
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join('\t'), // Use tabs for Excel
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header] || '';
            return typeof value === 'string' ? value.replace(/\t/g, ' ') : value;
          }).join('\t')
        )
      ].join('\n');

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'application/vnd.ms-excel',
          'Content-Disposition': `attachment; filename="contacts-export-${new Date().toISOString().split('T')[0]}.xls"`,
        },
      });
    }

    return NextResponse.json(
      { success: false, error: 'Unsupported format' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error exporting contacts:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
